# 项目结构

## 根目录结构
```
augment0.516.0/
├── extension/                    # 主要扩展代码目录
│   ├── package.json             # 扩展配置和依赖
│   ├── README.md                # 项目说明文档
│   ├── CHANGELOG.md             # 变更日志
│   ├── icon.png                 # 扩展图标
│   ├── out/                     # 编译输出目录
│   ├── common-webviews/         # WebView相关资源
│   └── media/                   # 媒体资源文件
├── .gitignore                   # Git忽略文件配置
├── custom-features.js           # 自定义功能脚本
├── extensio.js                  # 扩展入口脚本
└── [Content_Types].xml          # VSIX包内容类型定义
```

## 关键目录说明

### extension/
- **package.json**: 扩展的主配置文件，包含所有命令、配置项、依赖等
- **out/**: TypeScript编译后的JavaScript文件输出目录
- **common-webviews/**: 包含各种WebView界面的HTML和资源文件
- **media/**: 图标、SVG等媒体资源文件

### 编译输出
- `extension/out/extension.js`: 主要的扩展入口文件
- `extension/out/runtime/`: 运行时依赖，包含leveldb等原生模块

### WebView资源
- `extension/common-webviews/`: 包含聊天、设置、Next Edit等界面的HTML文件和资源