# Augment VSCode Extension 项目概览

## 项目目的
这是一个名为"Augment 加💩版"的VSCode扩展项目，是一个AI编程助手平台，为专业软件工程师和大型代码库提供AI驱动的编程功能。

## 主要功能
- **Agent**: 完成任务、构建功能、解决生产问题的智能代理
- **Chat**: 智能聊天，深度集成，提供即时答案和项目规划
- **Next Edit**: 提供逐步编辑指导，跨代码、测试和文档进行修改
- **Instructions**: 使用自然语言直接在编辑器中添加或修改代码
- **Code Completions**: 快速、代码库感知的代码补全建议
- **MCP支持**: 支持100+工具的MCP集成

## 技术栈
- **主要语言**: TypeScript
- **运行时**: Node.js (>= 18.15.0)
- **平台**: VSCode Extension API
- **包管理器**: pnpm (版本9)
- **构建工具**: esbuild
- **测试框架**: Jest + WebdriverIO (E2E)
- **代码检查**: ESLint + Prettier

## 版本信息
- 当前版本: 0.666.0
- VSCode引擎要求: ^1.82.0
- 发布者: Augment