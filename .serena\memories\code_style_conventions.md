# 代码风格和约定

## 代码风格工具
项目使用以下工具来维护代码质量：

### ESLint配置
- 使用 `@typescript-eslint/eslint-plugin` 和 `@typescript-eslint/parser`
- 启用了 `eslint-plugin-jest` 用于测试文件
- 启用了 `eslint-plugin-unused-imports` 来清理未使用的导入

### Prettier配置
- 使用 `@trivago/prettier-plugin-sort-imports` 来自动排序导入语句
- 自动格式化代码风格

## TypeScript约定
- 使用TypeScript 5.5.3版本
- 严格的类型检查
- 目标平台：Node.js 18.15.0+

## 文件命名约定
从项目结构可以看出：
- TypeScript文件使用 `.ts` 扩展名
- 测试文件使用 `.test.ts` 或 `.test.js` 扩展名
- 配置文件通常使用 `.config.js` 或 `.config.ts`

## 导入/导出约定
- 使用ES6模块语法
- 导入语句会被Prettier自动排序

## 测试约定
- 使用Jest作为测试框架
- 测试文件放在与源文件相同的目录或专门的测试目录
- 使用 `@jest/globals` 提供全局测试函数

## 构建约定
- 使用esbuild进行快速构建
- 开发模式包含源码映射
- 生产模式进行代码压缩
- 输出目录为 `extension/out/`

## WebView开发约定
- WebView相关代码放在 `extension/common-webviews/` 目录
- 使用HTML文件作为WebView入口点
- 资源文件统一管理在assets目录

## 版本控制约定
- 使用Git进行版本控制
- 详细的.gitignore配置，排除构建产物和临时文件
- 保留pnpm-lock.yaml，忽略其他包管理器的锁文件