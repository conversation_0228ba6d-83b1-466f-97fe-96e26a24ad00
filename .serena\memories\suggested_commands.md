# 建议的开发命令

## 包管理器
项目使用 **pnpm** 作为包管理器，版本要求为9。

## 主要开发命令

### 构建相关
```bash
# 开发构建（带源码映射）
pnpm run build

# 生产构建（压缩优化）
pnpm run build:prod

# 监听模式（自动重新构建）
pnpm run watch
```

### 代码质量
```bash
# 运行所有检查
pnpm run lint

# 自动修复代码风格问题
pnpm run lint:fix

# 运行ESLint检查
pnpm run vscode:eslint

# 运行Prettier格式化
pnpm run vscode:prettier:fix
```

### 测试相关
```bash
# 运行所有测试
pnpm run test

# 运行单元测试
pnpm run vscode:jest

# 运行端到端测试
pnpm run test:e2e

# 修复并测试
pnpm run test:fix

# 调试模式测试
pnpm run test:debug
```

### 扩展打包
```bash
# 打包扩展（使用pnpm）
pnpm run package-extension-pnpm

# 生成版本信息
pnpm run embed-version-info
```

### WebView开发
```bash
# 构建WebView资源
pnpm run common-webviews:build
```

## Windows系统工具命令
由于项目在Windows系统上开发，建议使用以下命令：

### 基本文件操作
```cmd
# 列出文件
dir
ls  # 如果安装了Git Bash或WSL

# 查找文件
findstr "pattern" *.js
grep "pattern" *.js  # Git Bash/WSL

# 复制文件
copy source dest
cp source dest  # Git Bash/WSL

# 删除文件
del filename
rm filename  # Git Bash/WSL
```

### Git操作
```bash
git status
git add .
git commit -m "message"
git push
git pull
```