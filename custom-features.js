let vscode=require("vscode"),crypto=require("crypto");class AugmentCustomFeatures{constructor(){this.logger=this.createLogger(),this.isInitialized=!1}createLogger(){return{info:(e,...s)=>console.log("[AugmentCustom] "+e,...s),warn:(e,...s)=>console.warn("[AugmentCustom] "+e,...s),error:(e,...s)=>console.error("[AugmentCustom] "+e,...s),debug:(e,...s)=>console.debug("[AugmentCustom] "+e,...s)}}async initialize(e,s=null){if(this.isInitialized)this.logger.warn("Custom features already initialized");else try{this.context=e,this.augmentExtension=s,this.registerCommands(),this.isInitialized=!0,this.logger.info("Custom features initialized successfully")}catch(e){throw this.logger.error("Failed to initialize custom features:",e),e}}registerCommands(){var e=[{id:"augment.custom.newpool",handler:()=>this.handleNewPool()}];e.forEach(e=>{e=vscode.commands.registerCommand(e.id,e.handler);this.context.subscriptions.push(e)}),this.logger.info(`Registered ${e.length} custom commands`)}async getAccessToken(){try{var e=await this.context.secrets.get("augment.sessions");if(!e)return{success:!1,error:"未找到会话数据"};try{var s=JSON.parse(e);return{success:!0,accessToken:s.accessToken,tenantURL:s.tenantURL,data:s}}catch(e){return this.logger.error("Failed to parse sessions data:",e),{success:!1,error:"解析会话数据失败"}}}catch(e){return this.logger.error("Failed to get access token:",e),{success:!1,error:e.message}}}async setSecret(s,e){try{var t="string"==typeof e?e:JSON.stringify(e);return await this.context.secrets.store(s,t),this.logger.info(`Secret ${s} stored successfully`),!0}catch(e){return this.logger.error(`Failed to store secret ${s}:`,e),!1}}async updateAccessToken(e){try{var t=await this.context.secrets.get("augment.sessions");let s={};if(t)try{s=JSON.parse(t)}catch(e){this.logger.warn("Failed to parse existing sessions data, creating new object"),s={}}return s.accessToken=e,s.tenantURL||(s.tenantURL="https://d5.api.augmentcode.com/"),s.scopes||(s.scopes=["email"]),await this.setSecret("augment.sessions",s)?(this.logger.info("AccessToken updated successfully"),{success:!0,data:s}):{success:!1,error:"存储更新后的会话数据失败"}}catch(e){return this.logger.error("Failed to update access token:",e),{success:!1,error:e.message}}}async updateSessionsData(e,t){try{var a=await this.context.secrets.get("augment.sessions");let s={};if(a)try{s=JSON.parse(a)}catch(e){this.logger.warn("Failed to parse existing sessions data, creating new object"),s={}}return s.tenantURL=e,s.accessToken=t,s.scopes||(s.scopes=["email"]),await this.setSecret("augment.sessions",s)?(this.logger.info("Sessions data updated successfully"),{success:!0,data:s}):{success:!1,error:"存储更新后的会话数据失败"}}catch(e){return this.logger.error("Failed to update sessions data:",e),{success:!1,error:e.message}}}async handleNewPool(){try{var e=await vscode.window.showQuickPick([{label:"获取 accessToken",description:"查看当前的 accessToken 和 tenantURL",detail:"显示当前存储的认证信息，支持复制和查看完整数据"},{label:"设置 accessToken",description:"修改 accessToken 或 tenantURL",detail:"更新认证信息，支持仅更新 accessToken 或完整更新会话数据"},{label:"更新机器码",description:"重置设备唯一标识符",detail:"生成并更新当前设备的机器码标识"}],{placeHolder:"选择要执行的操作"});e&&("获取 accessToken"===e.label?await this.handleGetAccessToken():"设置 accessToken"===e.label?await this.handleSetToken():"更新机器码"===e.label&&await this.handleUpdateMachineCode())}catch(e){vscode.window.showErrorMessage("错误: "+e.message)}}async handleGetAccessToken(){try{var e,s,t,a=await this.getAccessToken();a.success?(e=`accessToken: ${a.accessToken&&16<a.accessToken.length?a.accessToken.substring(0,8)+"..."+a.accessToken.substring(a.accessToken.length-8):a.accessToken||"未设置"}
tenantURL: `+(a.tenantURL||"未设置"),"复制 accessToken"===(s=await vscode.window.showInformationMessage(e,"复制 accessToken","显示完整数据"))&&a.accessToken?(await vscode.env.clipboard.writeText(a.accessToken),vscode.window.showInformationMessage("accessToken 已复制到剪贴板")):"显示完整数据"===s&&(t=await vscode.workspace.openTextDocument({content:JSON.stringify(a.data,null,2),language:"json"}),await vscode.window.showTextDocument(t))):vscode.window.showErrorMessage("获取 accessToken 失败: "+a.error)}catch(e){vscode.window.showErrorMessage("错误: "+e.message)}}async handleSetToken(){try{var e=await vscode.window.showQuickPick([{label:"仅更新 accessToken",description:"只更新 augment.sessions 中的 accessToken",detail:"快速更新：仅修改 accessToken，保留 tenantURL 和权限范围"},{label:"更新会话数据",description:"更新 augment.sessions 中的 tenantURL 和 accessToken",detail:"完整更新：通过引导输入同时修改 tenantURL 和 accessToken"}],{placeHolder:"选择要更新的内容"});if(e)if("仅更新 accessToken"===e.label){let e="输入新的 accessToken...";try{var s,t,a=await this.context.secrets.get("augment.sessions");a&&(s=JSON.parse(a)).accessToken&&(t=s.accessToken,e=16<t.length?`当前: ${t.substring(0,8)}...`+t.substring(t.length-8):"当前: "+t)}catch(e){this.logger.debug("Failed to get current accessToken for placeholder:",e)}var o,n,c=await vscode.window.showInputBox({prompt:"输入新的 accessToken",placeHolder:e,password:!0,validateInput:e=>e&&0!==e.trim().length?e.length<10?"accessToken 长度似乎太短":null:"accessToken 不能为空"});c&&((o=await this.updateAccessToken(c.trim())).success?(vscode.window.showInformationMessage("accessToken 更新成功！"),"显示更新后的数据"===await vscode.window.showInformationMessage("accessToken 更新成功！","显示更新后的数据")&&(n=await vscode.workspace.openTextDocument({content:JSON.stringify(o.data,null,2),language:"json"}),await vscode.window.showTextDocument(n))):vscode.window.showErrorMessage("更新 accessToken 失败: "+o.error))}else{let e={accessToken:"",tenantURL:"https://d5.api.augmentcode.com/",scopes:["email"]};try{var r,i=await this.context.secrets.get("augment.sessions");i&&(r=JSON.parse(i),e={...e,...r})}catch(e){this.logger.debug("Failed to get current sessions data:",e)}var d,l,g,u,w=await vscode.window.showInputBox({prompt:"输入 tenantURL",placeHolder:"当前: "+e.tenantURL,value:e.tenantURL,validateInput:e=>{if(!e||0===e.trim().length)return"tenantURL 不能为空";try{return new URL(e),null}catch{return"请输入有效的URL (例如: https://d5.api.augmentcode.com/)"}}});w&&(d=16<e.accessToken.length?e.accessToken.substring(0,8)+"..."+e.accessToken.substring(e.accessToken.length-8):e.accessToken,l=await vscode.window.showInputBox({prompt:"输入 accessToken",placeHolder:"当前: "+d,password:!0,validateInput:e=>e&&0!==e.trim().length?e.length<10?"accessToken 长度似乎太短":null:"accessToken 不能为空"}))&&(g={...e,tenantURL:w.trim(),accessToken:l.trim()},await this.setSecret("augment.sessions",g)?(vscode.window.showInformationMessage("会话数据更新成功！"),"显示更新后的数据"===await vscode.window.showInformationMessage("会话数据更新成功！","显示更新后的数据")&&(u=await vscode.workspace.openTextDocument({content:JSON.stringify(g,null,2),language:"json"}),await vscode.window.showTextDocument(u))):vscode.window.showErrorMessage("更新会话数据失败"))}}catch(e){vscode.window.showErrorMessage("错误: "+e.message)}}async handleUpdateMachineCode(){try{var e=crypto.randomUUID();await this.context.globalState.update("sessionId",e),vscode.window.showInformationMessage(`sessionId更新成功！新值: ${e}，请重载窗口以生效`,"重载窗口").then(e=>{"重载窗口"===e&&vscode.commands.executeCommand("workbench.action.reloadWindow")})}catch(e){vscode.window.showErrorMessage("错误: "+e.message)}}dispose(){this.isInitialized=!1,this.logger.info("Custom features disposed")}}module.exports=AugmentCustomFeatures;