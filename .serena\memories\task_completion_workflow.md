# 任务完成工作流程

## 开发任务完成后应执行的步骤

### 1. 代码质量检查
```bash
# 运行代码检查和自动修复
pnpm run lint:fix

# 确保没有ESLint错误
pnpm run vscode:eslint

# 确保代码格式正确
pnpm run vscode:prettier
```

### 2. 构建验证
```bash
# 执行生产构建以确保没有构建错误
pnpm run build:prod

# 如果涉及WebView更改，重新构建WebView资源
pnpm run common-webviews:build
```

### 3. 测试执行
```bash
# 运行所有测试
pnpm run test

# 如果测试失败，使用修复命令
pnpm run test:fix

# 对于复杂更改，运行端到端测试
pnpm run test:e2e
```

### 4. 扩展功能验证
如果更改了扩展功能：
```bash
# 打包扩展进行本地测试
pnpm run package-extension-pnpm

# 在VSCode中安装并测试打包的扩展
```

### 5. 版本和文档更新
- 更新 `CHANGELOG.md` 记录更改
- 如果是重大更改，更新 `README.md`
- 考虑更新版本号（在package.json中）

### 6. Git提交前检查
```bash
# 确保所有文件都已正确添加
git status

# 检查差异
git diff

# 提交更改
git add .
git commit -m "描述性的提交信息"
```

## 特殊注意事项

### WebView更改
如果修改了WebView相关代码：
- 确保重新构建WebView资源
- 测试所有相关的UI功能
- 检查不同主题下的显示效果

### 扩展配置更改
如果修改了package.json中的contributes部分：
- 验证所有命令和配置项都能正常工作
- 测试键盘快捷键绑定
- 确保菜单项正确显示

### 依赖更新
如果添加或更新了依赖：
- 确保pnpm-lock.yaml已更新
- 验证新依赖不会与现有功能冲突
- 检查包大小是否合理

### 性能考虑
- 对于影响启动性能的更改，进行性能测试
- 确保异步操作正确处理
- 验证内存使用情况