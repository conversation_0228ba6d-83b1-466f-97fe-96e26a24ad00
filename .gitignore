# ====================
# VSCode Extension 相关
# ====================

# 构建输出目录
extension/out/runtime

extension/dist/
extension/build/

# 媒体文件（如果是生成的）
extension/media/

# 打包后的扩展文件
*.vsix
extension/*.vsix

# VSCode 扩展打包生成的文件
[Content_Types].xml
extension.vsixmanifest

# ====================
# Node.js 相关
# ====================

# 依赖目录
node_modules/
extension/node_modules/
extension/out/node_modules/

# 包管理器锁文件（保留 pnpm-lock.yaml，忽略其他）
package-lock.json
yarn.lock
npm-shrinkwrap.json

# pnpm 相关
.pnpm-debug.log*
.pnpm-store/

# ====================
# TypeScript 相关
# ====================

# 编译输出
*.tsbuildinfo
*.d.ts.map

# ====================
# 开发工具相关
# ====================

# IDE 配置
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json
.vscode/tasks.json
.idea/
*.swp
*.swo
*~

# 调试文件
*.log
*.log.*
logs/
debug.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ====================
# 操作系统相关
# ====================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ====================
# 临时文件和缓存
# ====================

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 缓存目录
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# 测试覆盖率
coverage/
.nyc_output/
*.lcov

# 测试相关
test-results/
test-output/
*.test.js.map
wdio-*.log

# Jest 相关
jest-results/
jest.config.js.bak

# ====================
# 环境和配置文件
# ====================

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 本地配置文件
.augment-hmr-env
config.local.json

# 项目特定的配置文件
package.json.bak
version

# ====================
# 构建和部署相关
# ====================

# Webpack 相关
.webpack/

# Rollup 相关
.rollup.cache/

# Vite 相关
.vite/

# 预构建文件
prebuilds/
extension/out/prebuilds/

# WebView 相关
extension/common-webviews/dist/
webviews/dist/

# 脚本生成的文件
scripts/generated/
*.generated.*

# ====================
# 版本控制相关
# ====================

# Git 相关
*.orig
*.rej
.git-rewrite/

# ====================
# 其他工具相关
# ====================

# Bazel 相关
bazel-*

# 编辑器备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 文档生成
docs/build/
.docusaurus/



extension/common-webviews/assets